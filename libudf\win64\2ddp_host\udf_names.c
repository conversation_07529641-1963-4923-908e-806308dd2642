/* This file generated automatically. */
/*          Do not modify.            */
#include "udf.h"
#include "prop.h"
#include "dpm.h"
extern DEFINE_ADJUST(adjust_fcn, domain);
extern DEFINE_SOURCE(gamma_source, c, t, dS, equ);
extern DEFINE_SOURCE(k_source, c, t, dS, equ);
extern DEFINE_SOURCE(omega_source, c, t, dS, eqn);
extern DEFINE_UDS_FLUX(gamma_flux, f, t, i);
extern DEFINE_UDS_UNSTEADY(gamma_unsteady, c, t, i, apu, su);
extern DEFINE_DIFFUSIVITY(gamma_diffusivity, c, t, i);
__declspec(dllexport) UDF_Data udf_data[] = {
{"adjust_fcn", (void(*)())adjust_fcn, UDF_TYPE_ADJUST},
{"gamma_source", (void(*)())gamma_source, UDF_TYPE_SOURCE},
{"k_source", (void(*)())k_source, UDF_TYPE_SOURCE},
{"omega_source", (void(*)())omega_source, UDF_TYPE_SOURCE},
{"gamma_flux", (void(*)())gamma_flux, UDF_TYPE_UDS_FLUX},
{"gamma_unsteady", (void(*)())gamma_unsteady, UDF_TYPE_UDS_UNSTEADY},
{"gamma_diffusivity", (void(*)())gamma_diffusivity, UDF_TYPE_DIFFUSIVITY},
};
__declspec(dllexport) int n_udf_data = sizeof(udf_data)/sizeof(UDF_Data);
#include "version.h"
__declspec(dllexport) void UDF_Inquire_Release(int *major, int *minor, int *revision)
{
  *major = RampantReleaseMajor;
  *minor = RampantReleaseMinor;
  *revision = RampantReleaseRevision;
}
