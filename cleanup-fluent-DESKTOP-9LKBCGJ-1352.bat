echo off
set LOCALHOST=%COMPUTERNAME%
set KILL_CMD="C:\PROGRA~1\ANSYSI~1\v251\fluent/ntbin/win64/winkill.exe"

start "tell.exe" /B "C:\PROGRA~1\ANSYSI~1\v251\fluent\ntbin\win64\tell.exe" DESKTOP-9LKBCGJ 64279 CLEANUP_EXITING
timeout /t 1
"C:\PROGRA~1\ANSYSI~1\v251\fluent\ntbin\win64\kill.exe" tell.exe
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 13492) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 9836) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 8828) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 4808) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 2876) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 10672) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 15748) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12560) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 14456) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 11672) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 5220) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 13092) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 14864) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 5956) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 8328) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 15876) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 5040) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 9096) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 15424) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 18448) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 1756) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 16376) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 20184) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 6384) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12276) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12512) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12452) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12628) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 20064) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 6812) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 14672) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 20008) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 14340) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 19728) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12808) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12988) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12228) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12496) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12092) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 12028) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 11508) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 11720) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 9712) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 15784) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 9252) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 1352) 
if /i "%LOCALHOST%"=="DESKTOP-9LKBCGJ" (%KILL_CMD% 4752)
del "E:\onedrive\opencfd3D-20241024\fluent_udf\plate\udf\M1\cleanup-fluent-DESKTOP-9LKBCGJ-1352.bat"
