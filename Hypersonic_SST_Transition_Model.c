#include "udf.h"

/* Define which user-defined scalars to use. */
enum
{
    U<PERSON>,
    MAG_GRAD_Ue,
    N_REQUIRED_UDS
};

#define max(a, b) (((a) > (b)) ? (a) : (b))
#define min(a, b) (((a) < (b)) ? (a) : (b))
#define abs(x) ((x) >= 0 ? (x) : -(x))

/* Hypersonic flow parameters */
#define Mach 6.1        /* free stream Mach number*/
#define a 567.1         /* sound speed*/
#define V 3459.34       /* free stream velocity*/
#define rho 0.05267     /* free stream density*/
#define pressure 12100  /* free stream pressure*/
#define gamma 1.4       /* specific heat ratio*/
#define Tu 0.32         /* free stream turbulence intensity*/
#define Ren 0           /* blunt*/
#define Tw 300          /* temperature of wall*/
#define precession 1e-6 /* precision for iteration*/
#define gasconstan 287  /* specific gas constant for air J/(kg·K)*/

/* Hypersonic correction constants */
#define C_HYPER_1 0.85 /* Hypersonic correction factor 1 */
#define C_HYPER_2 1.25 /* Hypersonic correction factor 2 */
#define C_HYPER_3 0.75 /* Hypersonic correction factor 3 */
#define M_CRIT 3.0     /* Critical Mach number for hypersonic effects */

/* Pressure gradient parameter function */
static real Function_lambda(real lambda_theta)
{
    real F_lambda;

    if (lambda_theta > 0)
        F_lambda = 1.0 + 0.275 * (1.0 - exp(-35.0 * lambda_theta)) * exp(-Tu / 0.5);
    else
        F_lambda = 1.0 + (12.986 * lambda_theta + 123.66 * pow(lambda_theta, 2) + 405.689 * pow(lambda_theta, 3)) * exp(-pow(Tu / 1.5, 1.5));

    return F_lambda;
}

/* Turbulence intensity function */
static real Function_Tu(real x)
{
    real F_Tu;

    if (x > 1.3)
        F_Tu = 331.5 * pow(x - 0.5658, -0.671);
    else
        F_Tu = 1173.51 - 589.428 * x + 0.2196 * pow(x, -2);

    return F_Tu;
}

/* Zero pressure gradient correlation */
static real FZPG(real M, real T, real TWALL)
{
    real cr1, cr2, cr3;

    cr1 = (1.882e-4) * pow(M, 3) + (4.544e-3) * pow(M, 2) - (1.954e-1) * M + 1.748;
    cr2 = (1.667e-4) * pow(M, 3) - (2.171e-3) * pow(M, 2) - (2.937e-2) * M - 0.5902;
    cr3 = (-8.928e-4) * pow(M, 3) + (2.041e-2) * pow(M, 2) + (9.166e-2) * M + 0.4975;

    return cr1 * pow(T / TWALL, cr2) + cr3;
}

/* Pressure gradient correlation */
static real FPG(real M, real T, real TWALL, real lambda)
{
    real cr4, cr5;

    if (lambda > 0)
    {
        cr4 = -11.16 * exp(-0.5196 * M) - (5.215e-2) * exp(0.1180 * M);
        cr5 = (1.131e-3) * pow(M, 3) - (4.815e-2) * pow(M, 2) + 0.6370 * M - 2.307;
    }
    else
    {
        cr4 = -22.27 * exp(-0.5781 * M) - (4.430e-2) * exp(0.2658 * M);
        cr5 = (2.906e-2) * pow(M, 2) - 0.6513 * M + 0.4646;
    }

    return (cr4 * pow(T / TWALL, -2) + cr5) * lambda;
}

/* Turbulence intensity correlation */
static real Theta_Tu(real x)
{
    return 62.12 * pow(x + 0.032, -0.745);
}

/* Nose radius correlation */
static real fai_Ren(real x)
{
    real result;
    result = (1.0 + 1.0 / 3.5 * pow(x / 30000, 0.95)) * pow(1.0 + pow(x / 1400000.0, 10), -0.2);
    return 1.0 + 0.5 * (sqrt(result) - 1.0);
}

/* Hypersonic correction for intermittency onset */
static real Hypersonic_Correction_Onset(real Me, real Te, real Twall)
{
    real f_hyper = 1.0;

    if (Me > M_CRIT)
    {
        /* Temperature ratio effect */
        real temp_ratio = Te / Twall;

        /* Mach number effect */
        real mach_effect = 1.0 + C_HYPER_1 * (Me - M_CRIT) / M_CRIT;

        /* Temperature effect */
        real temp_effect = 1.0 + C_HYPER_2 * (temp_ratio - 1.0);

        /* Combined hypersonic correction */
        f_hyper = C_HYPER_3 * mach_effect * temp_effect;

        /* Limit the correction factor */
        f_hyper = min(max(f_hyper, 0.1), 3.0);
    }

    return f_hyper;
}

/* Hypersonic correction for destruction term */
static real Hypersonic_Correction_Destruction(real Me, real Re_theta)
{
    real f_dest = 1.0;

    if (Me > M_CRIT)
    {
        /* Enhanced destruction at high Mach numbers */
        real mach_factor = 1.0 + 0.5 * (Me - M_CRIT) / M_CRIT;

        /* Reynolds number effect */
        real re_factor = 1.0 + 0.1 * log(max(Re_theta, 1.0));

        f_dest = mach_factor * re_factor;

        /* Limit the correction factor */
        f_dest = min(max(f_dest, 0.5), 2.5);
    }

    return f_dest;
}

/* Calculate vorticity magnitude */
static real cal_vorticity(cell_t c, Thread *t)
{
    real vorticity;

#if RP_3D /*3D*/
    real wx = C_DWDY(c, t) - C_DVDZ(c, t);
    real wy = C_DUDZ(c, t) - C_DWDX(c, t);
    real wz = C_DVDX(c, t) - C_DUDY(c, t);
    vorticity = sqrt(wx * wx + wy * wy + wz * wz);
#else /*2D*/
    vorticity = abs(C_DVDX(c, t) - C_DUDY(c, t));
#endif

    return vorticity;
}

/* Compressibility correction for high-speed flows */
static real Compressibility_Correction(real Me, real Mt)
{
    real f_comp = 1.0;

    if (Me > 0.3)
    {
        /* Turbulent Mach number correction */
        real Mt_corr = min(Mt, 1.0);

        /* Compressibility function */
        f_comp = 1.0 - 0.2 * Mt_corr * Mt_corr;

        /* Additional correction for hypersonic flows */
        if (Me > M_CRIT)
        {
            real hyper_comp = 1.0 - 0.1 * (Me - M_CRIT) / M_CRIT;
            f_comp *= hyper_comp;
        }

        f_comp = max(f_comp, 0.1);
    }

    return f_comp;
}

/* DEFINE_ADJUST function to calculate edge velocity and its gradient */
DEFINE_ADJUST(adjust_fcn, domain)
{
    Thread *t;
    cell_t c;
    face_t f;

    /* Make sure there are enough user-defined scalars. */
    if (n_uds < N_REQUIRED_UDS)
        Internal_Error("not enough user-defined scalars allocated");

    /* Calculate edge velocity Ue */
    thread_loop_c(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)))
        {
            begin_c_loop(c, t)
            {
                real p_ratio = C_P(c, t) / pressure;
                if (p_ratio > 1e-10)
                {
                    real rho_e = rho * pow(p_ratio, 1.0 / gamma);
                    real term = pressure / rho - C_P(c, t) / rho_e;
                    if (term > 0)
                    {
                        C_UDSI(c, t, Ue) = sqrt(V * V + 2.0 * gamma / (gamma - 1.0) * term);
                    }
                    else
                    {
                        C_UDSI(c, t, Ue) = V;
                    }
                }
                else
                {
                    C_UDSI(c, t, Ue) = V;
                }
            }
            end_c_loop(c, t)
        }
    }

    /* Calculate edge velocity on faces */
    thread_loop_f(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)))
        {
            begin_f_loop(f, t)
            {
                real P = 0.0;
                if (NULL != THREAD_STORAGE(t, SV_P))
                    P = F_P(f, t);
                else if (NULL != THREAD_STORAGE(t->t0, SV_P))
                    P = C_P(F_C0(f, t), t->t0);

                real p_ratio = P / pressure;
                if (p_ratio > 1e-10)
                {
                    real rho_e = rho * pow(p_ratio, 1.0 / gamma);
                    real term = pressure / rho - P / rho_e;
                    if (term > 0)
                    {
                        F_UDSI(f, t, Ue) = sqrt(V * V + 2.0 * gamma / (gamma - 1.0) * term);
                    }
                    else
                    {
                        F_UDSI(f, t, Ue) = V;
                    }
                }
                else
                {
                    F_UDSI(f, t, Ue) = V;
                }
            }
            end_f_loop(f, t)
        }
    }

    /* Calculate streamwise gradient of edge velocity */
    thread_loop_c(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)) &&
            NULL != T_STORAGE_R_NV(t, SV_UDSI_G(Ue)))
        {
            begin_c_loop(c, t)
            {
                real vmag = C_VMAG2(c, t);
                if (vmag > 1e-10)
                {
#if RP_3D /*3D*/
                    C_UDSI(c, t, MAG_GRAD_Ue) = (C_UDSI_G(c, t, Ue)[0] * C_U(c, t) +
                                                 C_UDSI_G(c, t, Ue)[1] * C_V(c, t) +
                                                 C_UDSI_G(c, t, Ue)[2] * C_W(c, t)) /
                                                sqrt(vmag);
#else /*2D*/
                    C_UDSI(c, t, MAG_GRAD_Ue) = (C_UDSI_G(c, t, Ue)[0] * C_U(c, t) +
                                                 C_UDSI_G(c, t, Ue)[1] * C_V(c, t)) /
                                                sqrt(vmag);
#endif
                }
                else
                {
                    C_UDSI(c, t, MAG_GRAD_Ue) = 0.0;
                }
            }
            end_c_loop(c, t)
        }
    }

    /* Set gradient values on faces */
    thread_loop_f(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)) &&
            NULL != T_STORAGE_R_NV(t->t0, SV_UDSI_G(Ue)))
        {
            begin_f_loop(f, t)
            {
                F_UDSI(f, t, MAG_GRAD_Ue) = C_UDSI(F_C0(f, t), t->t0, MAG_GRAD_Ue);
            }
            end_f_loop(f, t)
        }
    }
}

/* Intermittency transport equation source term with hypersonic corrections */
DEFINE_SOURCE(gamma_source, c, t, dS, equ)
{
    real source;
    real intermittency = C_INTERMIT(c, t);
    real Rt = C_MU_T(c, t) / C_MU_L(c, t);
    real S = C_STRAIN_RATE_MAG(c, t);
    real d = C_WALL_DIST(c, t);
    real U = sqrt(C_VMAG2(c, t));
    real vorticity = cal_vorticity(c, t);
    real DUDs = C_UDSI(c, t, MAG_GRAD_Ue);

    /* Flow properties */
    real rho_e = rho * pow(C_P(c, t) / pressure, 1.0 / gamma);
    real Ue_local = C_UDSI(c, t, Ue);
    real ae = sqrt((V * V - Ue_local * Ue_local) * (gamma - 1.0) / 2.0 + a * a);
    real Me = Ue_local / ae;
    real Te = ae * ae / gamma / gasconstan;
    real twall = (Tw < 0) ? (1.0 + (gamma - 1.0) * sqrt(0.72) * pow(Me, 2) / 2.0) * Te : Tw;

    /* Turbulent Mach number for compressibility correction */
    real Mt = sqrt(2.0 * C_K(c, t)) / ae;

    /* Momentum thickness calculation with iteration */
    real lambda_theta = 0.0;
    real theta, theta_temp;
    real Re_v = C_R(c, t) * d * d * vorticity / C_MU_L(c, t);
    real F_ratio = FZPG(Me, Te, twall);

    theta = Re_v * C_MU_L(c, t) / C_R(c, t) / U / F_ratio;

    /* Iterative solution for momentum thickness */
    for (int i = 0; i < 50; i++)
    {
        theta_temp = theta;
        lambda_theta = (1.0 + 0.5 * (gamma - 1.0) * Me * Me) * C_R(c, t) * pow(theta, 2) / C_MU_L(c, t) * DUDs;
        lambda_theta = min(max(lambda_theta, -0.1), 0.1);

        F_ratio = FZPG(Me, Te, twall) + FPG(Me, Te, twall, lambda_theta);
        F_ratio = max(F_ratio, 0.1);

        real fun1 = theta_temp * F_ratio - Re_v * C_MU_L(c, t) / C_R(c, t) / U;
        real fun2 = F_ratio + 2.0 * FPG(Me, Te, twall, lambda_theta);

        theta = theta_temp - fun1 / fun2;

        if (abs(1.0 - theta_temp / theta) < precession)
            break;
    }

    /* Critical Reynolds number correlations */
    real Re_theta_t_high = Me * Theta_Tu(Tu) * fai_Ren(Ren * Mach) * Function_lambda(lambda_theta);
    real Re_theta_t_low = Function_Tu(Tu) * Function_lambda(lambda_theta);
    real Re_theta_t = (Mach > 1.2) ? Re_theta_t_high : Re_theta_t_low;
    real Re_theta_L = Re_v / F_ratio;

    /* Turbulence suppression function */
    real F_turb = exp(-pow(Rt / 2.0, 4));

    /* Onset function with hypersonic correction */
    real F_onset_1 = Re_theta_L / Re_theta_t / max(F_turb, 1e-20);
    real F_onset_2 = min(F_onset_1, 2.0);
    real F_onset_3 = max(1.0 - pow(Rt / 3.5, 3), 0.0);
    real F_onset = max(F_onset_2 - F_onset_3, 0.0);

    /* Apply hypersonic correction to onset function */
    // real f_hyper_onset = Hypersonic_Correction_Onset(Me, Te, twall);
    // F_onset *= f_hyper_onset;

    /* Apply compressibility correction */
    // real f_comp = Compressibility_Correction(Me, Mt);
    // F_onset *= f_comp;

    /* Intermittency production and destruction terms */
    real C1 = 100.0, C2 = 0.06, C3 = 50.0;

    /* Modified production term with hypersonic effects */
    real P_gamma = C_R(c, t) * C1 * S * F_onset * intermittency * (1.0 - intermittency);

    /* Enhanced destruction term with hypersonic correction */
    // real f_hyper_dest = Hypersonic_Correction_Destruction(Me, Re_theta_L);
    real D_gamma = C_R(c, t) * C2 * vorticity * F_turb * intermittency * (C3 * intermittency - 1.0);

    /* Additional hypersonic damping term */
    // real D_hyper = 0.0;
    // if (Me > M_CRIT)
    // {
    //     real hyper_factor = 0.1 * (Me - M_CRIT) / M_CRIT;
    //     D_hyper = C_R(c, t) * hyper_factor * intermittency * intermittency * S;
    // }

    source = P_gamma - D_gamma;

    /* Jacobian for implicit treatment */
    dS[equ] = C_R(c, t) * (C1 * S * F_onset * (1.0 - 2.0 * intermittency) - C2 * vorticity * F_turb * (2.0 * C3 * intermittency - 1.0));

    /* Store diagnostic variables */
    C_UDMI(c, t, 0) = F_onset_1;
    C_UDMI(c, t, 1) = lambda_theta;

    return source;
}

/* Modified k-equation source term with intermittency effects */
DEFINE_SOURCE(k_source, c, t, dS, equ)
{
    real source;
    real intermittency = C_INTERMIT(c, t);
    real Rt = C_MU_T(c, t) / C_MU_L(c, t);
    real S = C_STRAIN_RATE_MAG(c, t);
    real vorticity = cal_vorticity(c, t);
    real F_onset_1 = C_UDMI(c, t, 0);

    /* Flow properties for hypersonic correction */
    real Ue_local = C_UDSI(c, t, Ue);
    real ae = sqrt((V * V - Ue_local * Ue_local) * (gamma - 1.0) / 2.0 + a * a);
    real Me = Ue_local / ae;
    real Mt = sqrt(2.0 * C_K(c, t)) / ae;

    /* Standard k-equation terms with intermittency */
    real Pk = intermittency * C_MU_T(c, t) * vorticity * vorticity;
    real Dk = 0.09 * max(intermittency, 0.1) * C_R(c, t) * C_K(c, t) * C_O(c, t);

    /* Limit production term */
    Pk = min(Pk, 20.0 * Dk);

    /* Additional production term for separated flow regions */
    real Pks = 0.0;
    if (F_onset_1 > 1.0)
    {
        Pks = 5.0 * max(intermittency - 0.2, 0.0) * min(F_onset_1, 2.0) *
              max(3.0 * C_MU_L(c, t) - C_MU_T(c, t), 0.0) * vorticity * vorticity;
    }

    /* Compressibility correction for high-speed flows */
    // real f_comp = Compressibility_Correction(Me, Mt);
    // Pk *= f_comp;

    /* Hypersonic correction for destruction term */
    // if (Me > M_CRIT)
    // {
    //     real hyper_factor = 1.0 + 0.2 * (Me - M_CRIT) / M_CRIT;
    //     Dk *= hyper_factor;
    // }

    source = Pk - Dk;

    /* Jacobian */
    dS[equ] = -Dk / max(C_K(c, t), 1e-20);

    return source;
}

/* Modified omega-equation source term with compressibility effects */
DEFINE_SOURCE(omega_source, c, t, dS, eqn)
{
    real k = C_K(c, t);
    real omega = C_O(c, t);
    real S = C_STRAIN_RATE_MAG(c, t);
    real d = C_WALL_DIST(c, t);
    real dkdx, dkdy, dkdz, dwdx, dwdy, dwdz;
    real CD_kw, arg1, F1, F3, Ry;
    real gamma1 = 0.5532, gamma2 = 0.4403, beta1 = 0.075, beta2 = 0.0828, sigma_omega2 = 0.856;
    real Pw, Dw, CD, source, gam, beta;

    /* Flow properties for compressibility correction */
    real Ue_local = C_UDSI(c, t, Ue);
    real ae = sqrt((V * V - Ue_local * Ue_local) * (gamma - 1.0) / 2.0 + a * a);
    real Me = Ue_local / ae;
    real Mt = sqrt(2.0 * k) / ae;

    /* Cross-diffusion term */
#if RP_2D
    dkdx = C_K_G(c, t)[0];
    dkdy = C_K_G(c, t)[1];
    dwdx = C_O_G(c, t)[0];
    dwdy = C_O_G(c, t)[1];
    CD_kw = 2.0 * C_R(c, t) * sigma_omega2 * (dkdx * dwdx + dkdy * dwdy) / omega;
#else
    dkdx = C_K_G(c, t)[0];
    dkdy = C_K_G(c, t)[1];
    dkdz = C_K_G(c, t)[2];
    dwdx = C_O_G(c, t)[0];
    dwdy = C_O_G(c, t)[1];
    dwdz = C_O_G(c, t)[2];
    CD_kw = 2.0 * C_R(c, t) * sigma_omega2 * (dkdx * dwdx + dkdy * dwdy + dkdz * dwdz) / omega;
#endif

    CD_kw = max(CD_kw, 1.0e-20);

    /* Blending functions */
    arg1 = min(max(sqrt(k) / (0.09 * omega * d),
                   500.0 * C_MU_L(c, t) / (C_R(c, t) * omega * d * d)),
               4.0 * C_R(c, t) * sigma_omega2 * k / (CD_kw * d * d));
    F1 = tanh(arg1 * arg1 * arg1 * arg1);

    Ry = C_R(c, t) * d * sqrt(k) / C_MU_L(c, t);
    F3 = exp(-pow(Ry / 120.0, 8));
    F1 = max(F1, F3);

    /* Model constants */
    gam = F1 * gamma1 + (1.0 - F1) * gamma2;
    beta = F1 * beta1 + (1.0 - F1) * beta2;

    /* Production and destruction terms */
    Pw = gam * C_R(c, t) * vorticity * vorticity;
    Dw = beta * C_R(c, t) * omega * omega;
    CD = (1.0 - F1) * CD_kw;

    /* Apply compressibility correction */
    // real f_comp = Compressibility_Correction(Me, Mt);
    // Pw *= f_comp;

    /* Hypersonic correction for destruction */
    // if (Me > M_CRIT)
    // {
    //     real hyper_factor = 1.0 + 0.15 * (Me - M_CRIT) / M_CRIT;
    //     Dw *= hyper_factor;
    // }

    source = Pw - Dw + CD;

    /* Jacobian */
    dS[eqn] = -2.0 * beta * C_R(c, t) * omega;

    return source;
}

/* Modified turbulent viscosity with hypersonic corrections */
DEFINE_TURBULENT_VISCOSITY(user_mu_t, c, t)
{
    real dist1, dist2;
    real a1 = 0.31;
    real arg1, arg3, arg4;
    real blendtmp, denom;
    real vorticity = cal_vorticity(c, t);
    real mu_t;

    /* Flow properties for hypersonic correction */
    real Ue_local = C_UDSI(c, t, Ue);
    real ae = sqrt((V * V - Ue_local * Ue_local) * (gamma - 1.0) / 2.0 + a * a);
    real Me = Ue_local / ae;
    real Mt = sqrt(2.0 * C_K(c, t)) / ae;

    dist1 = abs(C_WALL_DIST(c, t));
    dist2 = dist1 * dist1;

    /* SST blending function arguments */
    arg1 = 500.0 * C_MU_L(c, t) / (C_R(c, t) * C_O(c, t) * dist2);
    arg3 = sqrt(C_K(c, t)) / (0.09 * C_O(c, t) * dist1);
    arg4 = max(2.0 * arg3, arg1);

    blendtmp = tanh(arg4 * arg4);
    denom = max(a1 * C_O(c, t), vorticity * blendtmp);

    /* Basic turbulent viscosity */
    mu_t = a1 * C_R(c, t) * C_K(c, t) / max(denom, 1e-20);

    /* Apply compressibility correction */
    real f_comp = Compressibility_Correction(Me, Mt);
    mu_t *= f_comp;

    /* Hypersonic correction for turbulent viscosity */
    if (Me > M_CRIT)
    {
        real temp_ratio = ae * ae / gamma / gasconstan / Tw;
        real hyper_factor = 1.0 - 0.1 * (Me - M_CRIT) / M_CRIT * (temp_ratio - 1.0);
        hyper_factor = max(hyper_factor, 0.1);
        mu_t *= hyper_factor;
    }

    /* Limit turbulent viscosity */
    mu_t = max(min(mu_t, 1.0e5), 1.0e-20);

    return mu_t;
}

/* Flux function for intermittency transport */
DEFINE_UDS_FLUX(gamma_flux, f, t, i)
{
    cell_t c0, c1 = -1;
    Thread *t0, *t1 = NULL;
    real NV_VEC(psi), NV_VEC(A), flux = 0.0;

    c0 = F_C0(f, t);
    t0 = F_C0_THREAD(f, t);
    F_AREA(A, f, t);

    if (BOUNDARY_FACE_THREAD_P(t))
    {
        real dens;
        if (NNULLP(THREAD_STORAGE(t, SV_DENSITY)))
            dens = F_R(f, t);
        else
            dens = C_R(c0, t0);

        NV_DS(psi, =, C_U(c0, t0), C_V(c0, t0), C_W(c0, t0), *, dens);
        flux = NV_DOT(psi, A);
    }
    else
    {
        c1 = F_C1(f, t);
        t1 = F_C1_THREAD(f, t);
        NV_DS(psi, =, C_U(c0, t0), C_V(c0, t0), C_W(c0, t0), *, C_R(c0, t0));
        NV_DS(psi, +=, C_U(c1, t1), C_V(c1, t1), C_W(c1, t1), *, C_R(c1, t1));
        flux = NV_DOT(psi, A) / 2.0;
    }

    return flux;
}

/* Unsteady term for intermittency transport */
DEFINE_UDS_UNSTEADY(gamma_unsteady, c, t, i, apu, su)
{
    real physical_dt, vol, r, fre_old;

    physical_dt = RP_Get_Real("physical-time-step");
    vol = C_VOLUME(c, t);
    r = C_R_M1(c, t);

    *apu = -r * vol / physical_dt;
    fre_old = C_STORAGE_R(c, t, SV_UDSI_M1(i));
    *su = r * vol * fre_old / physical_dt;
}

/* Diffusivity for intermittency transport */
DEFINE_DIFFUSIVITY(gamma_diffusivity, c, t, i)
{
    /* Effective diffusivity = molecular + turbulent */
    return C_MU_L(c, t) + C_MU_T(c, t);
}

/* Initialize intermittency field */
DEFINE_INIT(init_intermittency, domain)
{
    Thread *t;
    cell_t c;

    thread_loop_c(t, domain)
    {
        begin_c_loop(c, t)
        {
            /* Initialize intermittency to small value in freestream */
            C_UDSI(c, t, 0) = 1e-6;
        }
        end_c_loop(c, t)
    }
}
