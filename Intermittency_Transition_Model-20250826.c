#include "udf.h"

/* Define which user-defined scalars to use. */
enum
{
    U<PERSON>,
    MAG_GRAD_Ue,
    N_REQUIRED_UDS
};

#define max(a, b) (((a) > (b)) ? (a) : (b))
#define min(a, b) (((a) < (b)) ? (a) : (b))
#define Mach 6.1        /* free stream Mach number*/
#define a 567.1         /* sound speed*/
#define V 3459.34       /* free stream velocity*/
#define rho 0.05267     /* free stream density*/
#define pressure 12100  /* free stream pressure*/
#define Tu 0.32         /* free stream turbulence intensity*/
#define Ren 0           /* blunt*/
#define Tw 300          /* temperature of wall*/
#define gamma 1.4       /* specific heat ration*/
#define precession 1e-6 /* precession for iteration*/
#define gasconstan 287  /* specific gas constant for air J/(kg·K)*/

static real Function_lambda(real lambda_theta)
{
    real F_lambda;

    if (lambda_theta > 0)
        F_lambda = 1.0 + 0.275 * (1.0 - exp(-35.0 * lambda_theta)) * exp(-Tu / 0.5);
    else
        F_lambda = 1.0 + (12.986 * lambda_theta + 123.66 * pow(lambda_theta, 2) + 405.689 * pow(lambda_theta, 3)) * exp(-pow(Tu / 1.5, 1.5));

    return F_lambda;
}

static real Function_Tu(real x)
{
    real F_Tu;

    if (x > 1.3)
        F_Tu = 331.5 * pow(x - 0.5658, -0.671);
    else
        F_Tu = 1173.51 - 589.428 * x + 0.2196 * pow(x, -2);

    return F_Tu;
}

static real cal_cr4(real lambda_theta, real Me)
{
    real x;

    if (lambda_theta > 0)
        x = -11.16 * exp(-0.5196 * Me) - (5.215e-2) * exp(0.1180 * Me);
    else
        x = -22.27 * exp(-0.5781 * Me) - (4.430e-2) * exp(0.2658 * Me);

    return x;
}

static real cal_cr5(real lambda_theta, real Me)
{
    real x;

    if (lambda_theta > 0)
        x = (1.131e-3) * pow(Me, 3) - (4.815e-2) * pow(Me, 2) + 0.6370 * Me - 2.307;
    else
        x = (2.906e-2) * pow(Me, 2) - 0.6513 * Me + 0.4545;

    return x;
}

static real Fratio(real Me, real Te, real T_w, real lambda_theta)
{
    real cr1, cr2, cr3, cr4, cr5;
    real fratio, t;

    cr1 = (1.882e-4) * pow(Me, 3) + (4.544e-3) * pow(Me, 2) - (1.954e-1) * Me + 1.748;

    cr2 = (1.667e-4) * pow(Me, 3) - (2.171e-3) * pow(Me, 2) - (2.937e-2) * Me - 0.5902;

    cr3 = (-8.928e-4) * pow(Me, 3) + (2.041e-2) * pow(Me, 2) + (9.166e-2) * Me + 0.4975;

    cr4 = cal_cr4(lambda_theta, Me);

    cr5 = cal_cr5(lambda_theta, Me);

    fratio = cr1 * pow(Te / T_w, cr2) + cr3 + (cr4 * pow(Te / T_w, -2) + cr5) * lambda_theta;

    fratio = max(fratio, 0.1);

    return fratio;
}

static real Theta_Tu(real x)
{
    return 62.12 * pow(x + 0.032, -0.745);
}

static real fai_Ren(real x)
{
    real result;
    result = (1 + 1 / 3.5 * pow(x / 30000, 0.95)) * pow(1 + pow(x / 1400000, 10), -0.2);
    return 1 + 0.5 * (sqrt(result) - 1);
}

static real cal_vorticity(cell_t c, Thread *t)
{
    real vorticity, vorticity_streamwise;
#if RP_3D /*3D*/
    real wx = C_DWDY(c, t) - C_DVDZ(c, t), wy = C_DUDZ(c, t) - C_DWDX(c, t), wz = C_DVDX(c, t) - C_DUDY(c, t);

    vorticity = sqrt(wx * wx + wy * wy + wz * wz);

    // /*****calculate streamwise vorticity magnitude*****/
    // real vs_x, vs_y, vs_z;

    // vs_x = pow(C_U(c, t) / U * wx, 2);

    // vs_y = pow(C_V(c, t) / U * wy, 2);

    // vs_z = pow(C_W(c, t) / U * wz, 2);

    // vorticity_streamwise = sqrt(vs_x + vs_y + vs_z);

#else /*2D*/
    /*****calculate vorticity magnitude*****/
    vorticity = C_DVDX(c, t) - C_DUDY(c, t);

#endif
    return vorticity;
}
DEFINE_ADJUST(adjust_fcn, domain)
{
    Thread *t;
    cell_t c;
    face_t f;
    /* Make sure there are enough user-defined scalars. */
    if (n_uds < N_REQUIRED_UDS)
        Internal_Error("not enough user-defined scalars allocated");
    /* Fill first UDS with temperature raised to fourth power. */
    thread_loop_c(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)))
        {
            begin_c_loop(c, t)
            {
                real p_ratio = C_P(c, t) / pressure;
                if (p_ratio > 1e-10)
                {
                    real rho_e = rho * pow(p_ratio, 1 / gamma);
                    real term = pressure / rho - C_P(c, t) / rho_e;
                    if (term > 0)
                    {
                        C_UDSI(c, t, Ue) = sqrt(V * V + 2 * gamma / (gamma - 1) * term);
                    }
                    else
                    {
                        C_UDSI(c, t, Ue) = V; // If term is negative, set Ue to V
                    }
                }
                else
                {
                    C_UDSI(c, t, Ue) = V;
                }
            }
            end_c_loop(c, t)
        }
    }
    thread_loop_f(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)))
        {
            begin_f_loop(f, t)
            {
                real P = 0.;
                real rho_e;
                if (NULL != THREAD_STORAGE(t, SV_P))
                    P = F_P(f, t);
                else if (NULL != THREAD_STORAGE(t->t0, SV_P))
                    P = C_P(F_C0(f, t), t->t0);

                real p_ratio = P / pressure;
                if (p_ratio > 1e-10)
                {
                    rho_e = rho * pow(p_ratio, 1 / gamma);
                    real term = pressure / rho - P / rho_e;
                    if (term > 0)
                    {
                        F_UDSI(f, t, Ue) = sqrt(V * V + 2 * gamma / (gamma - 1) * term);
                    }
                    else
                    {
                        F_UDSI(f, t, Ue) = V;
                    }
                }
                else
                {
                    F_UDSI(f, t, Ue) = V;
                }
            }
            end_f_loop(f, t)
        }
    }
    /* Fill second UDS with magnitude of gradient. */
    thread_loop_c(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)) &&
            NULL != T_STORAGE_R_NV(t, SV_UDSI_G(Ue)))
        {
            begin_c_loop(c, t)
            {
                // C_UDSI(c, t, MAG_GRAD_Ue) = NV_MAG(C_UDSI_G(c, t, Ue));
                real vmag = C_VMAG2(c, t);
                if (vmag > 1e-10)
                {
#if RP_3D /*3D*/
                    C_UDSI(c, t, MAG_GRAD_Ue) = (C_UDSI_G(c, t, Ue)[0] * C_U(c, t) +
                                                 C_UDSI_G(c, t, Ue)[1] * C_V(c, t) +
                                                 C_UDSI_G(c, t, Ue)[2] * C_W(c, t)) /
                                                sqrt(vmag);
#else /*2D*/
                    C_UDSI(c, t, MAG_GRAD_Ue) = (C_UDSI_G(c, t, Ue)[0] * C_U(c, t) +
                                                 C_UDSI_G(c, t, Ue)[1] * C_V(c, t)) /
                                                sqrt(vmag);
#endif
                }
                else
                {
                    C_UDSI(c, t, MAG_GRAD_Ue) = 0.0;
                }
            }
            end_c_loop(c, t)
        }
    }
    thread_loop_f(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)) &&
            NULL != T_STORAGE_R_NV(t->t0, SV_UDSI_G(Ue)))
        {
            begin_f_loop(f, t)
            {
                F_UDSI(f, t, MAG_GRAD_Ue) = C_UDSI(F_C0(f, t), t->t0, MAG_GRAD_Ue);
            }
            end_f_loop(f, t)
        }
    }
}

DEFINE_SOURCE(gamma_source, c, t, dS, equ)
{
    real source;
    real intermittency = C_INTERMIT(c, t); /* C_INTERMIT(c, t)*/
    real Rt = C_MU_T(c, t) / C_MU_L(c, t); /* local viscosity ratio*/
    real S = C_STRAIN_RATE_MAG(c, t);      /* strain rate magnitude*/
    real d = C_WALL_DIST(c, t);            /* distance to nearest wall*/
    real U = sqrt(C_VMAG2(c, t));          /* velocity magnitude*/
    real Re_theta_t, Re_theta_c, Re_theta_L, Re_theta_t_high, Re_theta_t_low;
    real Re_v, F_turb;
    real vorticity = cal_vorticity(c, t);
    real lambda_theta, theta, theta_temp;
    real Ue, ae, Me, Te, rho_e, twall;
    real cr1, cr2, cr3, cr4, cr5, Fzpg, Fpg;
    real F_onset, F_onset_1, F_onset_2, F_onset_3, F_onset_4, F_onset_s, F_onset_cf, F_ratio, f_lim;
    real P_gamma, D_gamma, PG, G1, G2;
    real C1 = 100, C2 = 0.06, C3 = 50;
    real DUDs = C_UDSI(c, t, MAG_GRAD_Ue);

    /****Parameters of the outer edge of the boundary layer****/
    // Ue = sqrt(C_VMAG2(c, t));
    //  Message("The value of  Ue is % g\n", Ue);
    rho_e = rho * pow(C_P(c, t) / pressure, 1 / gamma);

    Ue = sqrt(V * V + 2 * gamma / (gamma - 1) * (pressure / rho - C_P(c, t) / rho_e));

    ae = sqrt((V * V - Ue * Ue) * (gamma - 1) / 2 + a * a);

    Me = Ue / ae;

    Te = ae * ae / gamma / gasconstan;

    if (Tw < 0)
    {
        twall = (1 + (gamma - 1) * sqrt(0.72) * pow(Me, 2) / 2) * Te;
    }
    else
    {
        twall = Tw;
    }

    cr1 = (1.882e-4) * pow(Me, 3) + (4.544e-3) * pow(Me, 2) - (1.954e-1) * Me + 1.748;

    cr2 = (1.667e-4) * pow(Me, 3) - (2.171e-3) * pow(Me, 2) - (2.937e-2) * Me - 0.5902;

    cr3 = (-8.928e-4) * pow(Me, 3) + (2.041e-2) * pow(Me, 2) + (9.166e-2) * Me + 0.4975;

    Fzpg = cr1 * pow(Te / twall, cr2) + cr3;

    /****theta****/
    // lambda_theta = 0;

    Re_v = C_R(c, t) * d * d * vorticity / C_MU_L(c, t);

    theta = Re_v / Fzpg * C_MU_L(c, t) / C_R(c, t) / U;

    for (int i = 0; i < 100; i++)
    {
        real fun1, fun2;

        lambda_theta = (1 + (gamma - 1) / 2 * pow(Me, 2)) * C_R(c, t) * pow(theta, 2) / C_MU_L(c, t) * DUDs;

        lambda_theta = min(max(lambda_theta, -0.1), 0.1);

        cr4 = cal_cr4(lambda_theta, Me);

        cr5 = cal_cr5(lambda_theta, Me);

        Fpg = (cr4 * pow(Te / twall, -2) + cr5) * lambda_theta;

        F_ratio = Fzpg + Fpg;

        F_ratio = max(F_ratio, 0.1);

        theta_temp = theta;

        /*****calculate lambda_theta**************/
        fun1 = theta_temp * F_ratio - Re_v * C_MU_L(c, t) / C_R(c, t) / U;

        fun2 = F_ratio + 2 * Fpg;

        theta = theta_temp - fun1 / fun2;

        if (ABS(theta_temp - theta) < precession)
            break;
    }

    C_UDMI(c, t, 1) = lambda_theta;

    Re_theta_t_high = Me * Theta_Tu(Tu) * fai_Ren(Ren * Mach) * Function_lambda(lambda_theta);

    Re_theta_t_low = Function_Tu(Tu) * Function_lambda(lambda_theta);

    F_turb = exp(-pow(Rt / 2.0, 4));

    if (Mach > 1.2)
        Re_theta_t = Re_theta_t_high;
    else
        Re_theta_t = Re_theta_t_low;

    Re_theta_L = Re_v / Fzpg;

    F_onset_1 = Re_theta_L / Re_theta_t / F_turb;

    C_UDMI(c, t, 0) = F_onset_1;

    F_onset_2 = min(F_onset_1, 2.0);

    F_onset_3 = max(1 - pow(Rt / 3.5, 3), 0);

    F_onset = max(F_onset_2 - F_onset_3, 0);

    // Message("The value of F_onset is  % d\n", F_onset);

    P_gamma = C_R(c, t) * C1 * S * F_onset * intermittency * (1.0 - intermittency);

    D_gamma = C_R(c, t) * C2 * vorticity * F_turb * intermittency * (C3 * intermittency - 1.0);

    source = P_gamma - D_gamma;

    dS[equ] = C_R(c, t) * C1 * S * F_onset * (1.0 - 2.0 * intermittency) - C_R(c, t) * C2 * vorticity * F_turb * (2.0 * C3 * intermittency - 1.0);

    return source;
}

DEFINE_SOURCE(k_source, c, t, dS, equ)
{
    real source;
    real Pk, Dk, Pks;
    real intermittency = C_INTERMIT(c, t);
    real S = C_STRAIN_RATE_MAG(c, t);
    real vorticity = cal_vorticity(c, t);
    real F_onset_1 = C_UDMI(c, t, 0);

    Dk = 0.09 * max(intermittency, 0.1) * C_R(c, t) * C_K(c, t) * C_O(c, t);

    Pk = intermittency * C_MU_T(c, t) * S * S;

    // Pk = min(Pk, 10 * Dk);

    if (F_onset_1 < 1.0)
        Pks = 0.0;
    else
        Pks = 5.0 * max(intermittency - 0.2, 0.0) * min(F_onset_1, 2.0) * max(3.0 * C_MU_L(c, t) - C_MU_T(c, t), 0.0) * vorticity * vorticity;

    source = Pk - Dk + Pks;

    dS[equ] = -Dk / C_K(c, t);

    return source;
}

DEFINE_SOURCE(omega_source, c, t, dS, eqn)
{
    real k = C_K(c, t);
    real omega = C_O(c, t);
    real S = C_STRAIN_RATE_MAG(c, t);
    real vorticity = cal_vorticity(c, t);
    real d = C_WALL_DIST(c, t); /* distance to nearest wall*/
    real dkdx, dkdy, dkdz, dwdx, dwdy, dwdz;
    real CD_kw, arg1, F1, F3, Ry;
    real gamma1 = 0.5532, gamma2 = 0.4403, beta1 = 0.075, beta2 = 0.0828, sigma_omega2 = 0.856;
    real Pw, Dw, CD, source, gam, beta;

#if RP_2D
    /* Term to account for cross-diffusion in omega equation */
    dkdx = C_K_G(c, t)[0];
    dkdy = C_K_G(c, t)[1];
    dwdx = C_O_G(c, t)[0];
    dwdy = C_O_G(c, t)[1];
    CD_kw = 2.0 * C_R(c, t) * sigma_omega2 * (dkdx * dwdx + dkdy * dwdy) / omega;
#else
    dkdz = C_K_G(c, t)[2];
    dwdz = C_O_G(c, t)[2];
    CD_kw = 2.0 * C_R(c, t) * sigma_omega2 * (dkdx * dwdx + dkdy * dwdy + dkdz * dwdz) / omega;
#endif

    CD_kw = max(CD_kw, 1.0e-20);

    arg1 = min(max(sqrt(k) / (0.09 * omega * d),
                   500.0 * C_MU_L(c, t) / (C_R(c, t) * omega * d * d)),
               4.0 * C_R(c, t) * sigma_omega2 * k / (CD_kw * d * d));
    F1 = tanh(arg1 * arg1 * arg1 * arg1);

    Ry = C_R(c, t) * d * sqrt(k) / C_MU_L(c, t);

    F3 = exp(-pow(Ry / 120.0, 8));

    F1 = max(F1, F3);

    gam = F1 * gamma1 + (1.0 - F1) * gamma2;
    beta = F1 * beta1 + (1.0 - F1) * beta2;

    Pw = gam * C_R(c, t) * S * S;
    Dw = beta * C_R(c, t) * omega * omega;
    CD = (1.0 - F1) * CD_kw;

    source = Pw - Dw + CD;

    dS[eqn] = -2.0 * beta * C_R(c, t) * omega;

    return source;
}

DEFINE_UDS_FLUX(gamma_flux, f, t, i)
{
    cell_t c0, c1 = -1;
    Thread *t0, *t1 = NULL;
    real NV_VEC(psi), NV_VEC(A), flux = 0.0;
    c0 = F_C0(f, t);
    t0 = F_C0_THREAD(f, t);

    F_AREA(A, f, t);

    if (BOUNDARY_FACE_THREAD_P(t)) /*Most face values will be available*/
    {
        real dens;
        /* Depending on its BC, density may not be set on face thread*/
        if (NNULLP(THREAD_STORAGE(t, SV_DENSITY)))
            dens = F_R(f, t); /* Set dens to face value if available */
        else
            dens = C_R(c0, t0); /* else, set dens to cell value */
        NV_DS(psi, =, C_U(c0, t0), C_V(c0, t0), C_W(c0, t0), *, dens);
        flux = NV_DOT(psi, A); /* flux through Face */
    }
    else
    {
        c1 = F_C1(f, t); /* Get cell on other side of face */
        t1 = F_C1_THREAD(f, t);
        NV_DS(psi, =, C_U(c0, t0), C_V(c0, t0), C_W(c0, t0), *, C_R(c0, t0));
        NV_DS(psi, +=, C_U(c1, t1), C_V(c1, t1), C_W(c1, t1), *, C_R(c1, t1));
        flux = NV_DOT(psi, A) / 2.0; /* Average flux through face */
    }

    return flux;
}

DEFINE_UDS_UNSTEADY(gamma_unsteady, c, t, i, apu, su)
{
    real physical_dt, vol, r, fre_old;
    physical_dt = RP_Get_Real("physical-time-step");
    vol = C_VOLUME(c, t);
    r = C_R_M1(c, t);
    *apu = -rho * vol / physical_dt; /*implicit part */
    fre_old = C_STORAGE_R(c, t, SV_UDSI_M1(i));
    *su = r * vol * fre_old / physical_dt; /*explicit part*/
}

DEFINE_DIFFUSIVITY(gamma_diffusivity, c, t, i)
{
    return C_MU_L(c, t) + C_MU_T(c, t);
}
