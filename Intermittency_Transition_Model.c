#include "udf.h"

/* Define which user-defined scalars to use. */
enum
{
    U<PERSON>,
    MAG_GRAD_Ue,
    N_REQUIRED_UDS
};

#define max(a, b) (((a) > (b)) ? (a) : (b))
#define min(a, b) (((a) < (b)) ? (a) : (b))
#define abs(x) ((x) >= 0 ? (x) : -(x))
#define Mach 6.1       /* free stream Mach number*/
#define a 567.1        /* sound speed*/
#define V 3459.34      /* free stream velocity*/
#define rho 0.05267    /* free stream density*/
#define pressure 12100 /* free stream pressure*/
#define gamma 1.4      /* specific heat ration*/
#define Tu 0.32        /* free stream turbulence intensity*/
#define Ren 0          /* blunt*/
#define Tw 300         /* temperature of wall*/
#define precession 1e-6
#define gasconstan 287

static real Function_lambda(real lambda_theta)
{
    real F_lambda;

    if (lambda_theta > 0)
        F_lambda = 1.0 + 0.275 * (1.0 - exp(-35.0 * lambda_theta)) * exp(-Tu / 0.5);
    else
        F_lambda = 1.0 + (12.986 * lambda_theta + 123.66 * pow(lambda_theta, 2) + 405.689 * pow(lambda_theta, 3)) * exp(-pow(Tu / 1.5, 1.5));

    return F_lambda;
}

static real Function_Tu(real x)
{
    real F_Tu;

    if (x > 1.3)
        F_Tu = 331.5 * pow(x - 0.5658, -0.671);
    else
        F_Tu = 1173.51 - 589.428 * x + 0.2196 * pow(x, -2);

    return F_Tu;
}

static real FZPG(real M, real T, real TWALL)
{
    real cr1, cr2, cr3;

    cr1 = (1.882e-4) * pow(M, 3) + (4.544e-3) * pow(M, 2) - (1.954e-1) * M + 1.748;

    cr2 = (1.667e-4) * pow(M, 3) - (2.171e-3) * pow(M, 2) - (2.937e-2) * M - 0.5902;

    cr3 = (-8.928e-4) * pow(M, 3) + (2.041e-2) * pow(M, 2) + (9.166e-2) * M + 0.4975;

    return cr1 * pow(T / TWALL, cr2) + cr3;
}

static real FPG(real M, real T, real TWALL, real lambda)
{
    real cr4, cr5;

    if (lambda > 0)
    {
        cr4 = -11.16 * exp(-0.5196 * M) - (5.215e-2) * exp(0.1180 * M);
        cr5 = (1.131e-3) * pow(M, 3) - (4.815e-2) * pow(M, 2) + 0.6370 * M - 2.307;
    }
    else
    {
        cr4 = -22.27 * exp(-0.5781 * M) - (4.430e-2) * exp(0.2658 * M);
        cr5 = (2.906e-2) * pow(M, 2) - 0.6513 * M + 0.4646;
    }

    return (cr4 * pow(T / TWALL, -2) + cr5) * lambda;
}

static real Theta_Tu(real x)
{
    return 62.12 * pow(x + 0.032, -0.745);
}

static real fai_Ren(real x)
{
    real result;
    result = (1.0 + 1.0 / 3.5 * pow(x / 30000, 0.95)) * pow(1.0 + pow(x / 1400000.0, 10), -0.2);
    return 1.0 + 0.5 * (sqrt(result) - 1.0);
}

DEFINE_ADJUST(adjust_fcn, domain)
{
    Thread *t;
    cell_t c;
    face_t f;
    /* Make sure there are enough user-defined scalars. */
    if (n_uds < N_REQUIRED_UDS)
        Internal_Error("not enough user-defined scalars allocated");
    /* Fill first UDS with temperature raised to fourth power. */
    thread_loop_c(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)))
        {
            begin_c_loop(c, t)
            {
                real rho_e = rho * pow(C_P(c, t) / pressure, 1 / gamma);
                C_UDSI(c, t, Ue) = sqrt(V * V + 2 * gamma / (gamma - 1) * (pressure / rho - C_P(c, t) / rho_e));
            }
            end_c_loop(c, t)
        }
    }
    thread_loop_f(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)))
        {
            begin_f_loop(f, t)
            {
                real P = 0.;
                real rho_e;
                if (NULL != THREAD_STORAGE(t, SV_P))
                    P = F_P(f, t);
                else if (NULL != THREAD_STORAGE(t->t0, SV_P))
                    P = C_P(F_C0(f, t), t->t0);
                rho_e = rho * pow(P / pressure, 1 / gamma);
                F_UDSI(f, t, Ue) = sqrt(V * V + 2 * gamma / (gamma - 1) * (pressure / rho - P / rho_e));
            }
            end_f_loop(f, t)
        }
    }
    /* Fill second UDS with magnitude of gradient. */
    thread_loop_c(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)) &&
            NULL != T_STORAGE_R_NV(t, SV_UDSI_G(Ue)))
        {
            begin_c_loop(c, t)
            {
                // C_UDSI(c, t, MAG_GRAD_Ue) = NV_MAG(C_UDSI_G(c, t, Ue));
#if RP_3D /*3D*/
                C_UDSI(c, t, MAG_GRAD_Ue) = (C_UDSI_G(c, t, Ue)[0] * C_U(c, t) + C_UDSI_G(c, t, Ue)[1] * C_V(c, t) + C_UDSI_G(c, t, Ue)[2] * C_W(c, t)) / sqrt(C_VMAG2(c, t));
#else /*2D*/
                C_UDSI(c, t, MAG_GRAD_Ue) = (C_UDSI_G(c, t, Ue)[0] * C_U(c, t) + C_UDSI_G(c, t, Ue)[1] * C_V(c, t)) / sqrt(C_VMAG2(c, t));
#endif
            }
            end_c_loop(c, t)
        }
    }
    thread_loop_f(t, domain)
    {
        if (NULL != THREAD_STORAGE(t, SV_UDS_I(Ue)) &&
            NULL != T_STORAGE_R_NV(t->t0, SV_UDSI_G(Ue)))
        {
            begin_f_loop(f, t)
            {
                F_UDSI(f, t, MAG_GRAD_Ue) = C_UDSI(F_C0(f, t), t->t0, MAG_GRAD_Ue);
            }
            end_f_loop(f, t)
        }
    }
}

DEFINE_UDS_FLUX(gamma_flux, f, t, i)
{
    cell_t c0, c1 = -1;
    Thread *t0, *t1 = NULL;
    real NV_VEC(psi), NV_VEC(A), flux = 0.0;
    c0 = F_C0(f, t);
    t0 = F_C0_THREAD(f, t);

    F_AREA(A, f, t);

    if (BOUNDARY_FACE_THREAD_P(t)) /*Most face values will be available*/
    {
        real dens;
        /* Depending on its BC, density may not be set on face thread*/
        if (NNULLP(THREAD_STORAGE(t, SV_DENSITY)))
            dens = F_R(f, t); /* Set dens to face value if available */
        else
            dens = C_R(c0, t0); /* else, set dens to cell value */
        NV_DS(psi, =, C_U(c0, t0), C_V(c0, t0), C_W(c0, t0), *, dens);
        flux = NV_DOT(psi, A); /* flux through Face */
    }
    else
    {
        c1 = F_C1(f, t); /* Get cell on other side of face */
        t1 = F_C1_THREAD(f, t);
        NV_DS(psi, =, C_U(c0, t0), C_V(c0, t0), C_W(c0, t0), *, C_R(c0, t0));
        NV_DS(psi, +=, C_U(c1, t1), C_V(c1, t1), C_W(c1, t1), *, C_R(c1, t1));
        flux = NV_DOT(psi, A) / 2.0; /* Average flux through face */
    }

    return flux;
}

DEFINE_UDS_UNSTEADY(gamma_unsteady, c, t, i, apu, su)
{
    real physical_dt, vol, r, fre_old;
    physical_dt = RP_Get_Real("physical-time-step");
    vol = C_VOLUME(c, t);
    r = C_R_M1(c, t);
    *apu = -rho * vol / physical_dt; /*implicit part */
    fre_old = C_STORAGE_R(c, t, SV_UDSI_M1(i));
    *su = r * vol * fre_old / physical_dt; /*explicit part*/
}

DEFINE_DIFFUSIVITY(gamma_diffusivity, c, t, i)
{
    return C_MU_L(c, t) + C_MU_T(c, t);
}

DEFINE_SOURCE(gamma_source, c, t, dS, equ)
{
    real source;
    real intermittency = C_INTERMIT(c, t); /* C_INTERMIT(c, t)C_UDSI(c, t, 0)*/
    real Rt = C_MU_T(c, t) / C_MU_L(c, t); /* local viscosity ratio*/
    real S = C_STRAIN_RATE_MAG(c, t);      /* strain rate magnitude*/
    real d = C_WALL_DIST(c, t);            /* distance to nearest wall*/
    real U = sqrt(C_VMAG2(c, t));          /* velocity magnitude*/
    real Re_theta_t, Re_theta_c, Re_theta_L, Re_theta_t_high, Re_theta_t_low;
    real Re_v, F_turb;
    real vorticity, vorticity_streamwise;
    real lambda_theta, theta, theta_temp;
    real Ue, ae, Me, Te, rho_e, twall;
    real F_onset, F_onset_1, F_onset_2, F_onset_3, F_onset_4, F_onset_s, F_onset_cf, F_ratio, f_lim;
    real P_gamma, D_gamma, PG, G1, G2;
    real C1 = 100, C2 = 0.06, C3 = 50;
    /****Parameters of the outer edge of the boundary layer****/
    //  Message("The value of  Ue is % g\n", Ue);
    rho_e = rho * pow(C_P(c, t) / pressure, 1.0 / gamma);

    Ue = C_UDSI(c, t, Ue);

    ae = sqrt((V * V - Ue * Ue) * (gamma - 1.0) / 2.0 + a * a);

    Me = Ue / ae;

    Te = ae * ae / gamma / gasconstan;

    if (Tw < 0)
    {
        twall = (1.0 + (gamma - 1.0) * sqrt(0.72) * pow(Me, 2) / 2.0) * Te;
    }
    else
    {
        twall = Tw;
    }

#if RP_3D /*3D*/
    /*****calculate vorticity magnitude*****/
    real wx = C_DWDY(c, t) - C_DVDZ(c, t), wy = C_DUDZ(c, t) - C_DWDX(c, t), wz = C_DVDX(c, t) - C_DUDY(c, t);

    vorticity = sqrt(wx * wx + wy * wy + wz * wz);
    /*****calculate acceleration along the streamwise direction*****/
    real DUDx, DUDy, DUDz, DUDs;

    DUDx = 0.5 * pow(U * U, -0.5) * (2.0 * C_U(c, t) * C_DUDX(c, t) + 2.0 * C_V(c, t) * C_DVDX(c, t) + 2.0 * C_W(c, t) * C_DWDX(c, t));

    DUDy = 0.5 * pow(U * U, -0.5) * (2.0 * C_U(c, t) * C_DUDY(c, t) + 2.0 * C_V(c, t) * C_DVDY(c, t) + 2.0 * C_W(c, t) * C_DWDY(c, t));

    DUDz = 0.5 * pow(U * U, -0.5) * (2.0 * C_U(c, t) * C_DUDZ(c, t) + 2.0 * C_V(c, t) * C_DVDZ(c, t) + 2.0 * C_W(c, t) * C_DWDZ(c, t));

    // DUDs = (C_U(c, t) / U) * DUDx + (C_V(c, t) / U) * DUDy + (C_W(c, t) / U) * DUDz;
    // DUDs = (C_UDSI_G(c, t, Ue)[0]*C_U(c, t) + C_UDSI_G(c, t, Ue)[1]*C_V(c, t) + C_UDSI_G(c, t, Ue)[2]*C_W(c, t))/U;
    // DUDs = C_UDSI(c, t, MAG_GRAD_Ue);

    /*****calculate streamwise vorticity magnitude*****/
    real vs_x, vs_y, vs_z;

    vs_x = pow(C_U(c, t) / U * wx, 2);

    vs_y = pow(C_V(c, t) / U * wy, 2);

    vs_z = pow(C_W(c, t) / U * wz, 2);

    vorticity_streamwise = sqrt(vs_x + vs_y + vs_z);

#else /*2D*/
    /*****calculate vorticity magnitude*****/
    vorticity = C_DVDX(c, t) - C_DUDY(c, t);

    /*****calculate acceleration along the streamwise direction*****/
    real DUDx, DUDy, DUDs;

    DUDx = 0.5 * pow(U * U, -0.5) * (2.0 * C_U(c, t) * C_DUDX(c, t) + 2.0 * C_V(c, t) * C_DVDX(c, t));

    DUDy = 0.5 * pow(U * U, -0.5) * (2.0 * C_U(c, t) * C_DUDY(c, t) + 2.0 * C_V(c, t) * C_DVDY(c, t));

    // DUDs = (C_U(c, t) / U) * DUDx + (C_V(c, t) / U) * DUDy;
    // DUDs = (C_UDSI_G(c, t, Ue)[0] * C_U(c, t) + C_UDSI_G(c, t, Ue)[1] * C_V(c, t)) / U;
    // DUDs = C_UDSI(c, t, MAG_GRAD_Ue);

#endif

    DUDs = C_UDSI(c, t, MAG_GRAD_Ue);
    /****theta****/
    lambda_theta = 0;

    Re_v = C_R(c, t) * d * d * vorticity / C_MU_L(c, t);

    theta = Re_v * C_MU_L(c, t) / C_R(c, t) / U / FZPG(Me, Te, twall);

    for (int i = 0; i < 100; i++)
    {
        real fun1, fun2;

        theta_temp = theta;

        F_ratio = FZPG(Me, Te, twall) + FPG(Me, Te, twall, lambda_theta);

        F_ratio = max(F_ratio, 0.1);

        /*****calculate lambda_theta**************/
        fun1 = theta_temp * F_ratio - Re_v * C_MU_L(c, t) / C_R(c, t) / U;

        fun2 = F_ratio + 2.0 * FPG(Me, Te, twall, lambda_theta);

        theta = theta_temp - fun1 / fun2;

        lambda_theta = (1.0 + 0.5 * (gamma - 1.0) * Me * Me) * C_R(c, t) * pow(theta, 2) / C_MU_L(c, t) * DUDs;

        lambda_theta = min(max(lambda_theta, -0.1), 0.1);

        C_UDMI(c, t, 1) = lambda_theta;

        if (ABS(1 - theta_temp / theta) < precession)
            break;
    }
    // Message("The value of theta is % d and the value of F_ratio is % g\n", theta, F_ratio);
    /*****stream*****/
    // F_ratio = max(F_ratio, 0.1);
    Re_theta_t_high = Me * Theta_Tu(Tu) * fai_Ren(Ren * Mach) * Function_lambda(lambda_theta);

    Re_theta_t_low = Function_Tu(Tu) * Function_lambda(lambda_theta);

    F_turb = exp(-pow(Rt / 2.0, 4));

    if (Mach > 1.2)
        Re_theta_t = Re_theta_t_high;
    else
        Re_theta_t = Re_theta_t_low;

    Re_theta_L = Re_v / FZPG(Me, Te, twall);

    // Re_theta_c = max(Re_theta_t * (1 - intermittency), 1e-5);

    F_onset_1 = Re_theta_L / Re_theta_t / max(F_turb, 1e-20);
    C_UDMI(c, t, 0) = F_onset_1;

    /*****crossflow*****/
    // #if RP_3D /*3D*/
    //     real He, H_cf, delta_H_cf, Re_cfc;

    //     He = vorticity_streamwise;

    //     H_cf = d * He / U;

    //     delta_H_cf = H_cf * (1 + min(Rt, 0.3));

    //     Re_cfc = 58.56 * pow(Tu, -0.14);

    //     F_onset_cf = delta_H_cf * Re_v / F_ratio / Re_cfc;

    //     F_onset_1 = max(F_onset_s, F_onset_cf);
    // #else
    //     F_onset_1 = F_onset_s;
    // #endif

    // Message("The value of F_onset_s is  % g\n", F_onset_s);

    F_onset_2 = min(F_onset_1, 2.0);

    F_onset_3 = max(1 - pow(Rt / 3.5, 3), 0.0);

    // F_onset_4 = max(F_onset_2 - F_onset_3, 0);

    // f_lim = exp(1 - S / vorticity);

    F_onset = max(F_onset_2 - F_onset_3, 0.0);

    // Message("The value of F_onset is  % d\n", F_onset);

    P_gamma = C_R(c, t) * C1 * S * F_onset * intermittency * (1.0 - intermittency);

    D_gamma = C_R(c, t) * C2 * vorticity * F_turb * intermittency * (C3 * intermittency - 1.0);

    // PG = C_R(c, t) * vorticity * F_onset;

    // G1 = 1 - intermittency;

    // G2 = max(sqrt(-log(1 - intermittency)), 1e-5);

    // D_gamma = intermittency * P_gamma;

    source = P_gamma - D_gamma;

    dS[equ] = C_R(c, t) * (C1 * S * F_onset * (1.0 - 2.0 * intermittency) - C2 * vorticity * F_turb * (2.0 * C3 * intermittency - 1.0));

    return source;
}

DEFINE_SOURCE(k_source, c, t, dS, equ)
{
    real source;
    real Pk, Dk, Pks;
    real intermittency = C_INTERMIT(c, t); // C_INTERMIT(c, t);
    real Rt = C_MU_T(c, t) / C_MU_L(c, t); /* local viscosity ratio*/
    real S = C_STRAIN_RATE_MAG(c, t);      /* strain rate magnitude*/
    real d = C_WALL_DIST(c, t);            /* distance to nearest wall*/
    real U = sqrt(C_VMAG2(c, t));          /* velocity magnitude*/
    real vorticity, F_onset_1;

#if RP_3D /*3D*/
    /*****calculate vorticity magnitude*****/
    real wx = C_DWDY(c, t) - C_DVDZ(c, t), wy = C_DUDZ(c, t) - C_DWDX(c, t), wz = C_DVDX(c, t) - C_DUDY(c, t);

    vorticity = sqrt(wx * wx + wy * wy + wz * wz);

#else /*2D*/
    /*****calculate vorticity magnitude*****/
    vorticity = C_DVDX(c, t) - C_DUDY(c, t);

#endif
    F_onset_1 = C_UDMI(c, t, 0);

    Dk = 0.09 * max(intermittency, 0.1) * C_R(c, t) * C_K(c, t) * C_O(c, t);

    Pk = intermittency * C_MU_T(c, t) * pow(C_STRAIN_RATE_MAG(c, t), 2);

    Pk = min(Pk, 20.0 * Dk);

    if (F_onset_1 < 1.0)
        Pks = 0.0;
    else
        Pks = 5 * max(intermittency - 0.2, 0.0) * min(F_onset_1, 2.0) * max(3.0 * C_MU_L(c, t) - C_MU_T(c, t), 0.0) * pow(vorticity, 2);

    source = Pk - Dk; //+ Pks;

    dS[equ] = -Dk / C_K(c, t);

    return source;
}

DEFINE_TURBULENT_VISCOSITY(user_mu_t, c, t)
{
    real dist1, dist2;
    real a1 = 0.31;
    real arg1, arg3, arg4;
    real blendtmp, denom;
    real vorticity;
    real mu_t;

    dist1 = abs(C_WALL_DIST(c, t));
    dist2 = dist1 * dist1;

#if RP_3D /*3D*/
    /*****calculate vorticity magnitude*****/
    real wx = C_DWDY(c, t) - C_DVDZ(c, t), wy = C_DUDZ(c, t) - C_DWDX(c, t), wz = C_DVDX(c, t) - C_DUDY(c, t);

    vorticity = sqrt(wx * wx + wy * wy + wz * wz);

#else /*2D*/
    /*****calculate vorticity magnitude*****/
    vorticity = C_DVDX(c, t) - C_DUDY(c, t);

#endif

    arg1 = 500.0 * C_MU_L(c, t) / (C_R(c, t) * C_O(c, t) * dist2);
    arg3 = sqrt(C_K(c, t)) / (0.09 * C_O(c, t) * dist1);
    arg4 = max(2.0 * arg3, arg1);

    blendtmp = tanh(arg4 * arg4);
    denom = max(a1 * C_O(c, t), vorticity * blendtmp);

    mu_t = a1 * C_R(c, t) * C_K(c, t) / max(denom, 1e-20);
    mu_t = max(min(mu_t, 1.e5), 1e-20);

    return mu_t;
}